<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\AccessToken;

use App\Domain\OAuth2\Client\Client;
use App\Domain\OAuth2\Scope\Scope;
use App\Model\Database\Entity\AbstractEntity;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\AccessTokenEntityInterface;
use League\OAuth2\Server\Entities\ScopeEntityInterface;




#[ORM\Entity(repositoryClass: AccessTokenRepository::class)]
#[ORM\Table(name: 'api_access_token')]
class AccessToken extends AbstractEntity implements AccessTokenEntityInterface {





	#[ORM\Id]
	#[ORM\Column(type: 'string', nullable: false)]
	protected string $identifier;

	/**
	 * @var string[]
	 */
	#[ORM\Column(type: 'json', nullable: false)]
	protected array $scopes = [];

	/**
	 * @var DateTimeImmutable
	 */
	#[ORM\Column(type: 'datetime_immutable', nullable: false)]
	protected DateTimeImmutable $expiryDateTime;

	/**
	 * @var string|int|null
	 */
	#[ORM\Column(type: 'string', nullable: true)]
	protected string|int|null $userIdentifier;

	/**
	 * @var Client
	 */
	#[ORM\ManyToOne(targetEntity: Client::class)]
	#[ORM\JoinColumn(name: 'client_id', referencedColumnName: 'identifier', nullable: false, onDelete: 'CASCADE')]
	protected $client;

	/**
	 * @param Scope[] $scopes
	 * @param string|int|null $userIdentifier
	 */
	public function __construct(Client $clientEntity, array $scopes, string|int|null $userIdentifier = NULL) {
		$this->client = $clientEntity;
		$this->scopes = array_map(fn(Scope $scope) => $scope->getIdentifier(), $scopes);
		$this->userIdentifier = $userIdentifier;
	}

	/**
	 * @param ScopeEntityInterface $scope
	 */
	public function addScope(ScopeEntityInterface $scope) : void {
		$this->scopes[] = $scope->getIdentifier();
	}

	/**
	 * @return Scope[]
	 */
	public function getScopes() : array {
		return array_map(fn($scope) => new Scope($scope), $this->scopes);
	}

	public function getIdentifier() : string {
		return $this->identifier;
	}

	public function setIdentifier($identifier) : void {
		if (!is_string($identifier) && !is_int($identifier)) {
			throw new \InvalidArgumentException('Identifier must be string or int');
		}
		$this->identifier = (string) $identifier;
	}

	public function getExpiryDateTime() : DateTimeImmutable {
		return $this->expiryDateTime;
	}

	public function setExpiryDateTime(DateTimeImmutable $dateTime) : void {
		$this->expiryDateTime = $dateTime;
	}

	public function getUserIdentifier() : string|int|null {
		return $this->userIdentifier;
	}

	public function setUserIdentifier($identifier) : void {
		$this->userIdentifier = $identifier;
	}

	public function getClient() : Client {
		return $this->client;
	}

	public function setClient($client) : void {
		if (!$client instanceof Client) {
			throw new \InvalidArgumentException('Client must be instance of Client');
		}
		$this->client = $client;
	}

	/**
	 * @param mixed $privateKey
	 */
	public function setPrivateKey($privateKey) : void {
		// This method is required by the interface but not used in our implementation
	}

	public function __toString() : string {
		return $this->identifier;
	}

}