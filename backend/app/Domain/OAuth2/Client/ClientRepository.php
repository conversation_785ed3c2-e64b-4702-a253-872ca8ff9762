<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Client;

use App\Model\Database\Repository\AbstractRepository;
use League\OAuth2\Server\Repositories\ClientRepositoryInterface;


/**
 * @method Client|null find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method Client|null findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL)
 * @method Client[] findAll()
 * @method Client[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 * @extends AbstractRepository<Client>
 */
class ClientRepository extends AbstractRepository implements ClientRepositoryInterface {

	/**
	 * @inheritDoc
	 */
	public function getClientEntity($clientIdentifier) : ?Client {
		return $this->find($clientIdentifier);
	}

	/**
	 * @inheritDoc
	 */
	public function validateClient($clientIdentifier, $clientSecret, $grantType) : bool {
		$client = $this->findOneBy(['identifier' => $clientIdentifier]);

		if ($client === NULL) {
			return FALSE;
		}

		if ($client->isConfidential() && $client->getSecret() !== $clientSecret) {
			return FALSE;
		}

		return TRUE;
	}
}