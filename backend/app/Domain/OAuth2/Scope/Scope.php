<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\Scope;

use App\Model\Database\Entity\AbstractEntity;
use League\OAuth2\Server\Entities\ScopeEntityInterface;



class Scope extends AbstractEntity implements ScopeEntityInterface {

	private string $identifier;

	public function __construct(string $identifier) {
		$this->identifier = $identifier;
	}

	public function getIdentifier() : string {
		return $this->identifier;
	}

	public function jsonSerialize() : string {
		return $this->identifier;
	}

}